#!/usr/bin/env python3
"""Simple test for NXDX parsing."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.parsers.niche_markup_parser import <PERSON><PERSON>MarkupPars<PERSON>

def test_simple_nxdx():
    """Test NXDX parsing with simple content."""
    
    # Simple NXDX content
    sample = """<Data>
    <Node Name="Occurrence">
        <Cell Name="OccurrenceFileNoG" Value="C202500802526"/>
        <Cell Name="Summary" Value="Test summary"/>
        <Node Name="GOccIvGPerson">
            <Node Name="GPersonName">
                <Cell Name="SurnameG" Value="PARK"/>
                <Cell Name="Given1G" Value="ANDREW"/>
            </Node>
        </Node>
    </Node>
</Data>"""
    
    parser = NicheMarkupParser()
    metadata = {'category': 'niche_markup', 'real_type': 'nxdx'}
    
    result = parser.parse(sample.encode('utf-8'), metadata)
    
    print("✅ NXDX PARSING TEST")
    print("=" * 30)
    print(f"Success: {result['parsing_successful']}")
    print(f"Text length: {result['text_length']}")
    print()
    print("Extracted text:")
    print("-" * 20)
    print(result['normalized_text'])

if __name__ == "__main__":
    test_simple_nxdx()
