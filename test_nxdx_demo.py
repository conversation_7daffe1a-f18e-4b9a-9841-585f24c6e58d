#!/usr/bin/env python3
"""Demo script to test improved NXDX parsing."""

import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.parsers.niche_markup_parser import <PERSON><PERSON>MarkupPars<PERSON>


def test_nxdx_parsing():
    """Test NXDX parsing with the actual file."""
    
    # Read the actual NXDX file
    nxdx_file = r'c:\Users\<USER>\Desktop\2.nxdx'
    
    try:
        with open(nxdx_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"NXDX file not found: {nxdx_file}")
        return
    
    print("=" * 60)
    print("NXDX PARSER IMPROVEMENT DEMO")
    print("=" * 60)
    
    # Initialize parser
    parser = NicheMarkupParser()
    metadata = {'category': 'niche_markup', 'real_type': 'nxdx'}
    
    print(f"Original file size: {len(content)} characters")
    print(f"Original content preview (first 200 chars):")
    print(content[:200] + "...")
    print()
    
    # Parse the content
    try:
        result = parser.parse(content.encode('utf-8'), metadata)
        
        if result['parsing_successful']:
            text = result['normalized_text']
            
            print("✅ PARSING SUCCESSFUL!")
            print(f"Extracted text length: {len(text)} characters")
            print(f"Compression ratio: {len(content)/len(text):.1f}:1")
            print()
            
            print("📋 EXTRACTED CONTENT:")
            print("-" * 40)
            
            # Split into lines and show key information
            lines = [line.strip() for line in text.split('\n') if line.strip()]
            
            # Show section headers
            sections = [line for line in lines if line.startswith('===')]
            print("📁 SECTIONS FOUND:")
            for section in sections:
                print(f"  {section}")
            print()
            
            # Show key-value pairs
            key_values = [line for line in lines if ':' in line and not line.startswith('===')]
            print("🔑 KEY INFORMATION EXTRACTED:")
            for kv in key_values[:15]:  # Show first 15
                print(f"  {kv}")
            if len(key_values) > 15:
                print(f"  ... and {len(key_values) - 15} more")
            print()
            
            # Check for artifacts
            artifacts = []
            if '<' in text or '>' in text:
                artifacts.append("XML tags found")
            if 'ReferenceId' in text:
                artifacts.append("Reference IDs found")
            if 'LeafSpecialization' in text:
                artifacts.append("Leaf specializations found")
            if 'undefined' in text:
                artifacts.append("Undefined values found")
            
            if artifacts:
                print("⚠️  REMAINING ARTIFACTS:")
                for artifact in artifacts:
                    print(f"  - {artifact}")
            else:
                print("✅ NO XML ARTIFACTS REMAINING!")
            
            print()
            print("📊 CONTENT ANALYSIS:")
            print("-" * 40)
            
            # Analyze content
            if "OCCURRENCE DETAILS" in text:
                print("✅ Main occurrence information extracted")
            if "File No:" in text:
                print("✅ File number extracted")
            if "RETAIL THEFT" in text:
                print("✅ Occurrence type extracted")
            if "ANDREW" in text and "PARK" in text:
                print("✅ Person information extracted")
            if "MILTON" in text:
                print("✅ Location information extracted")
            if "REAL CANADIAN SUPERSTORE" in text:
                print("✅ Business information extracted")
            if "105.99" in text:
                print("✅ Property values extracted")
            
            print()
            print("📄 FULL EXTRACTED TEXT:")
            print("-" * 40)
            print(text)
            
        else:
            print("❌ PARSING FAILED!")
            print(f"Error: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ EXCEPTION DURING PARSING: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_nxdx_parsing()
