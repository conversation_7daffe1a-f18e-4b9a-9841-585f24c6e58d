"""Tests for NXDX parser improvements."""

import pytest
from src.parsers.niche_markup_parser import NicheMarkupParser


class TestNXDXParser:
    """Test cases for improved NXDX parsing."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.parser = NicheMarkupParser()
        
        # Sample NXDX content based on the actual file structure
        self.sample_nxdx = """<Data>
        <Node Name="Occurrence">
                <Cell Name="OccurrenceFileNoG" Value="C202500802526"/>
                <Cell Name="OccurrenceStdOccTypeRId" Value="95800111000000000011033"/><Cell Name="OccurrenceStdOccTypeRId_L" Value="917 - RETAIL THEFT [2143.0000] From: 2009/01/01 00:00"/><Cell Name="Reportable" Value="1"/>
                <Cell Name="ReportedTimeTZV2G" Value="2025/06/16 18:23"/><Cell Name="StartTimeTZV2G" Value="2025/06/16 16:51"/><Cell Name="EndTimeTZV2G" Value="2025/06/16 17:27"/><Cell Name="Summary" Value="I <PERSON> Park am employed by the Real Canadian Superstore as an Asset Protection Representative (APR) located at 820 Main Street East, Milton, ON, now referred to as the Writer will state: On Monday, June 16, 2025, at approximately 16:51 hours, the Writer observed via CCTV (Closed Circuit Television) a Male in the meat department."/>
                <Node Name="GOccIvPA">
                        <Cell Name="UCRLocationTypeG" Value="12F"/><Cell Name="ClassificationG" Value=";OLC;"/><Node Name="PhysicalAddress">
                                <Cell Name="CivicSiteStreetNumberG" Value="820"/>
                                <Cell Name="StreetNameG" Value="MAIN STREET EAST"/>
                                <Cell Name="MunicipalityNameG" Value="MILTON"/>
                                <Cell Name="ProvStateCodeG" Value="ON"/>
                        </Node>
                </Node>
                <Node Name="GOccIvGPerson">
                        <Cell Name="ClassificationG" Value=";RPP;CMP;"/><Node Name="GPerson" LeafSpecialization="Person">
                                <Node Name="GPersonName">
                                        <Cell Name="SurnameG" Value="PARK"/>
                                        <Cell Name="Given1G" Value="ANDREW"/>
                                        <Cell Name="GenderG" Value="M"/><Cell Name="DateOfBirthG" Value="1998/03/18"/>
                                </Node>
                                <Node Name="GPersonSubjectEmployment">
                                        <Cell Name="EmployerG" Value="REAL CANADIAN SUPERSTORE"/>
                                </Node>
                        </Node>
                </Node>
        </Node>
</Data>"""
    
    def test_parse_nxdx_structure(self):
        """Test parsing of NXDX structure."""
        metadata = {'category': 'niche_markup', 'real_type': 'nxdx'}
        
        result = self.parser.parse(self.sample_nxdx.encode('utf-8'), metadata)
        
        assert result['parsing_successful'] is True
        text = result['normalized_text']
        
        # Check that main sections are properly extracted
        assert "OCCURRENCE DETAILS" in text
        assert "PHYSICAL ADDRESS" in text or "GOccIvPA" in text
        assert "PERSON INFORMATION" in text or "GOccIvGPerson" in text
        
        # Check that key data is extracted
        assert "C202500802526" in text  # File number
        assert "2025/06/16 18:23" in text  # Reported time
        assert "RETAIL THEFT" in text  # Occurrence type
        assert "PARK" in text  # Surname
        assert "ANDREW" in text  # Given name
        assert "MILTON" in text  # Municipality
        assert "MAIN STREET EAST" in text  # Street name
        assert "REAL CANADIAN SUPERSTORE" in text  # Employer
        
    def test_nxdx_field_name_cleaning(self):
        """Test field name cleaning."""
        # Test various field name patterns
        test_cases = [
            ("OccurrenceFileNoG", "File Number"),
            ("ReportedTimeTZV2G", "Reported Time"),
            ("CivicSiteStreetNumberG", "Street Number"),
            ("DateOfBirthG", "Date of Birth"),
            ("ProvStateCodeG", "Province"),
            ("GOccIvPA", "Physical Address"),
            ("GOccIvGPerson", "Person Information"),
        ]
        
        for input_name, expected in test_cases:
            result = self.parser._clean_field_name(input_name)
            assert result == expected, f"Expected '{expected}' but got '{result}' for input '{input_name}'"
    
    def test_nxdx_occurrence_processing(self):
        """Test occurrence node processing."""
        from bs4 import BeautifulSoup
        
        # Create a simple occurrence node
        xml_content = """<Node Name="Occurrence">
            <Cell Name="OccurrenceFileNoG" Value="C202500802526"/>
            <Cell Name="Summary" Value="Test summary"/>
        </Node>"""
        
        soup = BeautifulSoup(xml_content, 'xml')
        occurrence_node = soup.find('Node')
        
        sections = self.parser._process_nxdx_occurrence(occurrence_node)
        
        assert len(sections) >= 1
        occurrence_section = sections[0]
        assert "OCCURRENCE DETAILS" in occurrence_section
        assert "File Number: C202500802526" in occurrence_section
        assert "Summary: Test summary" in occurrence_section
    
    def test_nxdx_nested_node_processing(self):
        """Test nested node processing."""
        from bs4 import BeautifulSoup
        
        xml_content = """<Node Name="GPersonName">
            <Cell Name="SurnameG" Value="PARK"/>
            <Cell Name="Given1G" Value="ANDREW"/>
        </Node>"""
        
        soup = BeautifulSoup(xml_content, 'xml')
        node = soup.find('Node')
        
        result = self.parser._process_nxdx_node(node)
        
        assert "NAME" in result
        assert "Surname: PARK" in result
        assert "Given1: ANDREW" in result
    
    def test_nxdx_cleanup_patterns(self):
        """Test NXDX pattern cleanup."""
        test_content = """Some text
        ReferenceId="123"
        LeafSpecialization="Person"
        <Node Name="test">
        undefined"""
        
        result = self.parser._clean_nxdx_patterns(test_content)
        
        assert "ReferenceId" not in result
        assert "LeafSpecialization" not in result
        assert "<Node" not in result
        assert "undefined" not in result
        assert "Some text" in result
    
    def test_full_nxdx_parsing_integration(self):
        """Test full integration of NXDX parsing."""
        metadata = {'category': 'niche_markup', 'real_type': 'nxdx'}
        
        result = self.parser.parse(self.sample_nxdx.encode('utf-8'), metadata)
        
        assert result['parsing_successful'] is True
        text = result['normalized_text']
        
        # Verify no XML artifacts remain
        assert "<" not in text
        assert ">" not in text
        assert "ReferenceId" not in text
        assert "LeafSpecialization" not in text
        
        # Verify meaningful content is preserved and structured
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        # Should have section headers
        section_headers = [line for line in lines if line.startswith('===')]
        assert len(section_headers) >= 2  # At least 2 main sections
        
        # Should have key-value pairs
        key_value_pairs = [line for line in lines if ':' in line and not line.startswith('===')]
        assert len(key_value_pairs) >= 5  # Should have several key-value pairs
        
        # Should preserve important details
        assert any("PARK" in line for line in lines)
        assert any("ANDREW" in line for line in lines)
        assert any("MILTON" in line for line in lines)
        assert any("C202500802526" in line for line in lines)
    
    def test_nxdx_vs_nrt_detection(self):
        """Test that NXDX files are properly detected and processed differently from NRT."""
        nxdx_metadata = {'category': 'niche_markup', 'real_type': 'nxdx'}
        nrt_sample = "{ntf1{p1l{b TEST}}}"
        
        # NXDX content should be processed as XML
        nxdx_result = self.parser.parse(self.sample_nxdx.encode('utf-8'), nxdx_metadata)
        
        # NRT content should be processed with bracket parsing
        nrt_metadata = {'category': 'niche_markup', 'real_type': 'nrt'}
        nrt_result = self.parser.parse(nrt_sample.encode('utf-8'), nrt_metadata)
        
        # Both should succeed but use different parsing methods
        assert nxdx_result['parsing_successful'] is True
        assert nrt_result['parsing_successful'] is True
        
        # NXDX should have structured XML-based output
        assert "OCCURRENCE DETAILS" in nxdx_result['normalized_text']
        
        # NRT should have bracket-based output
        assert "TEST" in nrt_result['normalized_text']
